# WebView批量图片获取 - 数字文件夹支持

## 📁 功能概述

WebView资源拦截的批量图片获取功能已经完全支持数字文件夹结构。系统可以正确处理存储在不同数字文件夹中的图片资源。

## 🏗️ 文件夹结构

### 素材库目录结构
```
/data/data/com.airdoc.mpd/files/materials/
├── 123/                    # 数字文件夹
│   ├── eye_chart.jpg
│   ├── color_test.png
│   └── vision_test.gif
├── 456/                    # 另一个数字文件夹
│   ├── reading_test.jpg
│   └── contrast_test.png
└── 789/
    └── depth_test.jpg
```

### 其他支持的目录结构
```
/temp/
├── 001/
│   └── capture.jpg
└── 002/
    └── screenshot.png

/camera/
├── session_123/
│   └── photo.jpg
└── session_456/
    └── image.png
```

## 🔗 URL映射规则

| 图片类型 | 前端请求URL | 对应imageId | 物理路径 |
|---------|------------|-------------|----------|
| 素材库图片 | `/material/123/eye_chart.jpg` | `material_123/eye_chart.jpg` | `/materials/123/eye_chart.jpg` |
| 临时图片 | `/temp/001/capture.jpg` | `temp_001/capture.jpg` | `/temp/001/capture.jpg` |
| 相机图片 | `/camera/session_123/photo.jpg` | `camera_session_123/photo.jpg` | `/camera/session_123/photo.jpg` |
| 默认图片 | `/images/default.jpg` | `default.jpg` | `/files/default.jpg` |

## 📝 前端使用方法

### 1. 基本用法

```javascript
// 请求不同数字文件夹中的图片
const imageIds = [
    'material_123/eye_chart.jpg',      // 素材库123文件夹
    'material_456/color_test.png',     // 素材库456文件夹
    'temp_001/capture.jpg',            // 临时文件夹001
    'camera_session_123/photo.jpg'     // 相机会话123
];

// 获取图片URL列表
const imageListJson = android.getBatchImageUrls(JSON.stringify(imageIds));
const imageList = JSON.parse(imageListJson);

if (imageList.success) {
    console.log(`成功获取${imageList.count}个图片URL`);
    // 返回的URL格式：
    // [
    //   { id: "material_123/eye_chart.jpg", url: "/material/123/eye_chart.jpg" },
    //   { id: "material_456/color_test.png", url: "/material/456/color_test.png" },
    //   { id: "temp_001/capture.jpg", url: "/temp/001/capture.jpg" },
    //   { id: "camera_session_123/photo.jpg", url: "/camera/session_123/photo.jpg" }
    // ]
}
```

### 2. 完整的批量加载示例

```javascript
async function loadImagesFromDifferentFolders() {
    // 定义来自不同数字文件夹的图片
    const imageIds = [
        // 素材库123中的图片
        'material_123/eye_chart.jpg',
        'material_123/color_wheel.png',
        
        // 素材库456中的图片  
        'material_456/reading_test.jpg',
        'material_456/contrast_test.png',
        
        // 临时文件夹中的图片
        'temp_001/capture_left.jpg',
        'temp_002/capture_right.jpg',
        
        // 相机会话中的图片
        'camera_session_123/photo_001.jpg',
        'camera_session_123/photo_002.jpg'
    ];
    
    try {
        // 1. 获取所有图片的URL列表
        const imageListJson = android.getBatchImageUrls(JSON.stringify(imageIds));
        const imageList = JSON.parse(imageListJson);
        
        if (!imageList.success) {
            throw new Error(imageList.error);
        }
        
        console.log(`准备从${imageList.count}个不同文件夹加载图片`);
        
        // 2. 按文件夹分组显示
        const folderGroups = {};
        imageList.urls.forEach(imageInfo => {
            const folderId = imageInfo.id.split('/')[0];
            if (!folderGroups[folderId]) {
                folderGroups[folderId] = [];
            }
            folderGroups[folderId].push(imageInfo);
        });
        
        // 3. 并发加载所有图片
        const loadPromises = imageList.urls.map(imageInfo => {
            return new Promise((resolve, reject) => {
                const img = new Image();
                img.onload = () => {
                    console.log(`✅ 图片加载成功: ${imageInfo.id}`);
                    resolve({
                        id: imageInfo.id,
                        url: imageInfo.url,
                        element: img,
                        folder: imageInfo.id.split('/')[0]
                    });
                };
                img.onerror = () => {
                    console.error(`❌ 图片加载失败: ${imageInfo.id}`);
                    reject(new Error(`Failed to load: ${imageInfo.id}`));
                };
                
                // 设置图片源，触发shouldInterceptRequest拦截
                img.src = imageInfo.url;
            });
        });
        
        // 4. 等待所有图片加载完成
        const results = await Promise.all(loadPromises);
        
        // 5. 按文件夹分组显示图片
        const container = document.getElementById('imageContainer');
        
        Object.keys(folderGroups).forEach(folderId => {
            // 创建文件夹标题
            const folderTitle = document.createElement('h3');
            folderTitle.textContent = `文件夹: ${folderId}`;
            container.appendChild(folderTitle);
            
            // 创建文件夹容器
            const folderDiv = document.createElement('div');
            folderDiv.style.border = '1px solid #ccc';
            folderDiv.style.margin = '10px';
            folderDiv.style.padding = '10px';
            
            // 添加该文件夹的所有图片
            results
                .filter(result => result.folder === folderId)
                .forEach(result => {
                    result.element.style.width = '150px';
                    result.element.style.margin = '5px';
                    result.element.title = result.id;
                    folderDiv.appendChild(result.element);
                });
            
            container.appendChild(folderDiv);
        });
        
        console.log('🎉 所有文件夹的图片加载完成!');
        
    } catch (error) {
        console.error('❌ 批量加载图片失败:', error);
    }
}
```

### 3. 动态文件夹图片加载

```javascript
// 动态加载指定素材库文件夹的所有图片
async function loadMaterialFolder(materialId) {
    try {
        // 假设我们知道该文件夹中的图片列表
        const folderImages = [
            `material_${materialId}/image1.jpg`,
            `material_${materialId}/image2.png`,
            `material_${materialId}/image3.gif`
        ];
        
        const imageListJson = android.getBatchImageUrls(JSON.stringify(folderImages));
        const imageList = JSON.parse(imageListJson);
        
        if (imageList.success) {
            console.log(`素材库${materialId}中有${imageList.count}个图片`);
            
            // 加载图片...
            const loadPromises = imageList.urls.map(imageInfo => {
                return new Promise((resolve) => {
                    const img = new Image();
                    img.onload = () => resolve(img);
                    img.onerror = () => resolve(null);
                    img.src = imageInfo.url;
                });
            });
            
            const images = await Promise.all(loadPromises);
            const validImages = images.filter(img => img !== null);
            
            console.log(`成功加载${validImages.length}个图片`);
            return validImages;
        }
    } catch (error) {
        console.error(`加载素材库${materialId}失败:`, error);
        return [];
    }
}

// 使用示例
loadMaterialFolder(123).then(images => {
    images.forEach(img => {
        document.body.appendChild(img);
    });
});
```

## ⚡ 性能优化建议

1. **分批加载**: 对于大量图片，建议分批次加载
2. **预加载**: 可以预先获取URL列表，按需加载图片
3. **缓存策略**: 利用浏览器缓存，避免重复加载
4. **错误处理**: 对加载失败的图片进行重试或跳过

## 🔧 错误处理

```javascript
// 处理文件夹不存在的情况
const imageIds = ['material_999/nonexistent.jpg'];
const imageListJson = android.getBatchImageUrls(JSON.stringify(imageIds));
const imageList = JSON.parse(imageListJson);

if (imageList.success && imageList.count === 0) {
    console.warn('指定文件夹中没有找到图片');
} else if (!imageList.success) {
    console.error('获取图片列表失败:', imageList.error);
}
```

## 📋 总结

✅ **完全支持数字文件夹**：可以处理任意数字命名的文件夹结构
✅ **路径自动映射**：前端URL自动映射到正确的物理路径  
✅ **批量高效加载**：支持跨文件夹的批量图片加载
✅ **错误处理完善**：提供详细的错误信息和处理机制
✅ **性能优化**：利用浏览器并发能力和缓存机制
